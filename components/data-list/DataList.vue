<template>
  <view class="data-list-container">
    <!-- 搜索栏（可根据需要自定义） -->
    <slot name="search"></slot>

    <!-- 列表 -->
    <scroll-view
        scroll-y
        :style="scrollViewStyle"
        @scrolltolower="handleLoadMore"
        @refresherrefresh="handleRefresh"
        :refresher-enabled="true"
        :refresher-threshold="80"
        :refresher-default-style="'black'"
        :refresher-background="'#f8f8f8'"
        :refresher-triggered="refresherTriggered"
    >
      <!-- 顶部安全区域 -->
      <view class="safe-area-top"></view>

      <!-- 有滑动操作时使用 u-swipe-action -->
      <u-swipe-action v-if="hasSwipeActions">
        <u-swipe-action-item
            threshold="80"
            v-for="(item, index) in data"
            :key="index"
            :options="rightOptions"
            @click="onSwipeClick"
            :dataItem="item"
        >
          <view class="apply-list-item" @click="onItemClick" :data-index="index" :data-item="JSON.stringify(item)">
            <view v-for="label in labels" :key="label.prop" class="apply-list-row">
              <text class="apply-list-label">{{ label.label }}：</text>
              <text class="apply-list-value">{{ item[label.prop] }}</text>
            </view>
          </view>
          <template v-if="$slots['right']" #right>
            <slot name="right" :item="item" :index="index" />
          </template>
        </u-swipe-action-item>
      </u-swipe-action>

      <!-- 无滑动操作时直接渲染列表项 -->
      <template v-else>
        <view
            v-for="(item, index) in data"
            :key="getItemKey(item, index)"
            class="apply-list-item"
            @click="onItemClick"
            :data-index="index"
            :data-item="JSON.stringify(item)"
        >
          <view v-for="label in labels" :key="label.prop" class="apply-list-row">
            <text class="apply-list-label">{{ label.label }}：</text>
            <text class="apply-list-value">{{ item[label.prop] }}</text>
          </view>
        </view>
      </template>

      <view v-if="loading" class="loading">加载中...</view>
      <view v-if="!loading && data.length === 0" class="empty">暂无数据</view>
      <view v-if="hasMoreData" class="load-more-tip" @click="handleLoadMore">
        <text>点击加载更多</text>
        <text class="load-more-icon">↓</text>
      </view>
      <view v-if="!loading && data.length > 0 && pagination && data.length >= pagination.total" class="no-more">没有更多</view>

      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  name: "DataList",
  props: {
    labels: { type: Array, required: true },
    data: { type: Array, required: true },
    loading: { type: Boolean, default: false },
    pagination: { type: Object, default: null },
    rightOptions: { type: Array, default: () => [] },
    foreignKey:{type: String, default: 'id'},
    height: { type: String, default: 'auto' } // 允许外部传入高度
  },
  emits: ["search", "refresh", "load-more", "item-click", "swipe-action"],
  data() {
    return {
      refresherTriggered: false
    };
  },
  computed: {
    hasSwipeActions() {
      return this.rightOptions && this.rightOptions.length > 0;
    },
    scrollViewStyle() {
      if (this.height !== 'auto') {
        return `height: ${this.height}`;
      }
      // 计算可用高度，减去导航栏(88rpx)和搜索栏(约100rpx)的高度，确保最小高度
      return 'height: calc(100vh - 200rpx); min-height: 400rpx;';
    },
    // 判断是否还有更多数据可以加载
    hasMoreData() {
      return this.pagination &&
             this.data.length > 0 &&
             this.data.length < this.pagination.total &&
             !this.loading;
    }
  },
  methods: {
    key(e) {
      return e[this.foreignKey]
    },
    // 生成列表项的唯一key，兼容非H5平台
    getItemKey(item, index) {
      return item[this.foreignKey] || item.id || index;
    },
    handleRefresh() {
      this.refresherTriggered = true;
      this.$emit("refresh");
    },
    // 提供给父组件调用的方法，用于停止刷新状态
    stopRefresh() {
      this.refresherTriggered = false;
    },
    handleLoadMore() {
      this.$emit("load-more");
    },
    onItemClick(e) {
      let item = e.currentTarget.dataset.item;
      if (typeof item === 'string') {
        try { item = JSON.parse(item); } catch (err) {}
      }
      this.$emit("item-click", item);
    },
    onSwipeClick(e) {
      let item = e.dataItem;
      let index = e.index
      const action = e.item?.text;
      this.$emit('swipe-action', {action, item, index});
    },
  },
};
</script>

<style lang="scss" scoped>
.apply-list-item {
  background: #fff;
  margin: 16rpx 0;
  padding: 24rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx #eee;
}
.apply-list-row {
  display: flex;
  margin-bottom: 8rpx;
}
.apply-list-label {
  color: #888;
  min-width: 120rpx;
}
.apply-list-value {
  color: #222;
}
.loading, .empty {
  text-align: center;
  color: #888;
  margin: 32rpx 0;
}
.load-more-tip {
  text-align: center;
  color: #007aff;
  font-size: 28rpx;
  margin: 32rpx 16rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.load-more-tip:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.load-more-icon {
  font-size: 24rpx;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4rpx);
  }
}
.no-more {
  text-align: center;
  color: #bbb;
  font-size: 26rpx;
  margin: 32rpx 0 0 0;
}

.safe-area-top {
  height: 20rpx;
}

.safe-area-bottom {
  height: 120rpx; /* 为底部浮动按钮预留空间 */
}

.data-list-container {
  width: 100%;
}
</style>