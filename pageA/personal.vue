<template>
  <view>
    <Navbar :hideBtn="false" bgColor="#f3f4f6" title="个人资料编辑"></Navbar>

    <!-- 用户类型提示 -->
    <view v-if="!isGeneralUser" class="user-type-tips">
      <u-icon name="info-circle-fill" color="#ff9500" size="32"/>
      <text class="tips-text" v-if="isFieldDisabled">您当前为{{ userTypeDisplayText }}，个人信息已保存，仅可编辑学历信息</text>
      <text class="tips-text" v-else>您当前为{{ userTypeDisplayText }}，请完善并保存个人信息，保存后仅可编辑学历信息</text>
    </view>

    <view style="padding: 40rpx;">
      <u--form
        labelPosition="left"
        :model="formData"
        :rules="rules"
        ref="uForm"
        labelWidth="160rpx"
      >
        <u-form-item
          label="姓名"
          prop="realName"
          borderBottom
          required
        >
          <u--input
            v-model="formData.realName"
            placeholder="请输入姓名"
            border="none"
            clearable
            :disabled="isFieldDisabled"
            :disabledColor="isFieldDisabled ? '#f5f5f5' : '#ffffff'"
          ></u--input>
        </u-form-item>

        <u-form-item
          label="身份证"
          prop="idCard"
          borderBottom
          required
        >
          <u--input
            v-model="formData.idCard"
            placeholder="请输入身份证号"
            border="none"
            @blur="calculateGender"
            clearable
            :disabled="isFieldDisabled"
            :disabledColor="isFieldDisabled ? '#f5f5f5' : '#ffffff'"
          ></u--input>
          <u-button
            slot="right"
            size="mini"
            type="primary"
            @click="chooseIdCardImage"
            style="margin-left: 16rpx;"
            :disabled="isFieldDisabled"
          >识别</u-button>
        </u-form-item>

        <u-form-item
          label="性别"
          prop="sex"
          borderBottom
          required
          @click="handleGenderClick"
        >
          <u--input
            v-model="sexText"
            disabled
            :disabledColor="isFieldDisabled ? '#f5f5f5' : '#ffffff'"
            placeholder="请选择性别"
            border="none"
          ></u--input>
          <u-icon
            slot="right"
            name="arrow-right"
            :color="isFieldDisabled ? '#c8c9cc' : '#909399'"
          ></u-icon>
        </u-form-item>

        <u-form-item
          label="手机号码"
          prop="phonenumber"
          borderBottom
          required
        >
          <u--input
            v-model="formData.phonenumber"
            placeholder="请输入手机号码"
            border="none"
            clearable
            :disabled="isFieldDisabled"
            :disabledColor="isFieldDisabled ? '#f5f5f5' : '#ffffff'"
          ></u--input>
        </u-form-item>

        <u-form-item
          label="学历"
          prop="education"
          borderBottom
          @click="showEducationPicker = true; hideKeyboard()"
          required
        >
          <u--input
            v-model="educationText"
            disabled
            disabledColor="#ffffff"
            placeholder="请选择学历"
            border="none"
          ></u--input>
          <u-icon
            slot="right"
            name="arrow-right"
          ></u-icon>
        </u-form-item>

        <u-form-item
          label="工作单位"
          prop="companyName"
          borderBottom
        >
          <u--input
            v-model="formData.companyName"
            placeholder="请输入工作单位"
            border="none"
            clearable
            :disabled="isFieldDisabled"
            :disabledColor="isFieldDisabled ? '#f5f5f5' : '#ffffff'"
          ></u--input>
        </u-form-item>

<!--        <u-form-item
          label="二寸电子照"
          prop="photoUrl"
          borderBottom
          required
        >
          <view class="photo-upload">
            <view v-if="formData.photoUrl" class="preview-container">
              <image
                :src="formData.photoUrl"
                mode="aspectFill"
                class="preview-image"
                @click="previewImage"
              ></image>
              <view class="delete-btn" @click.stop="deletePhoto">
                <u-icon name="close" color="#ffffff" size="20"></u-icon>
              </view>
            </view>
            <view v-else class="upload-placeholder" @click="chooseImage">
              <u-icon name="camera-fill" size="40" color="#909399"></u-icon>
              <text class="upload-text">点击上传</text>
            </view>
          </view>
        </u-form-item>-->
      </u--form>
    </view>

    <view style="padding: 40rpx;">
      <u-row gutter="32">
        <u-col span="6">
          <u-button icon="arrow-left" text="返回" plain @click="goBack()"></u-button>
        </u-col>
        <u-col span="6">
          <u-button icon="checkmark-circle" text="保存" type="primary" @click="saveProfile"></u-button>
        </u-col>
      </u-row>
    </view>

    <!-- 性别选择器 -->
    <u-action-sheet
      :show="showSex"
      :actions="sexActions"
      title="请选择性别"
      @close="showSex = false"
      @select="sexSelect"
    >
    </u-action-sheet>

    <!-- 学历选择器 -->
    <u-picker
      :show="showEducationPicker"
      :columns="[educationOptions]"
      keyName="label"
      @confirm="onEducationConfirm"
      @cancel="showEducationPicker = false"
      :closeOnClickOverlay="true"
      @close="showEducationPicker = false"
    ></u-picker>
  </view>
</template>

<script>
import Navbar from '@/components/navbar/Navbar'
import storage from '@/utils/storage'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import { updatePersonalInfo, getPersonalInfo } from '@/api/user'
import { getDicts } from '@/api/system/dict/data'
import { loadUserTypeDict, getUserTypeText } from '@/utils/dictUtils.js'
import {uploadImage} from '@/utils/upload'

const { environment } = require('@/config/environment.js')
const baseUrl = environment.baseURL

let app = null

export default {
  components: {
    Navbar
  },
  data() {
    return {
      formData: {
        userId: '',
        realName: '',
        idCard: '',
        phonenumber: '',
        sex: '',
        education: '',
        companyName: '',
        photoUrl: '',
        photoId: ''
      },
      educationText: '',
      sexText: '',
      showSex: false,
      showEducationPicker: false,
      educationOptions: [],
      uploadStatus: false,
      sexActions: [
        { name: '男', value: 0 },
        { name: '女', value: 1 }
      ],
      rules: {
        realName: {
          type: 'string',
          required: true,
          message: '请输入姓名',
          trigger: ['blur', 'change']
        },
        idCard: [{
          type: 'string',
          required: true,
          message: '请输入身份证号',
          trigger: ['blur', 'change']
        }, {
          validator: (rule, value, callback) => {
            // 港澳台证件号格式校验
            const hkMacauReg = /^[HMhm]{1}([0-9]{10}|[0-9]{8})$/
            const taiwanReg = /^[0-9]{8}$/
            
            if (hkMacauReg.test(value) || taiwanReg.test(value)) {
              callback()
              return
            }
            
            // 大陆身份证号长度校验
            if (value.length !== 18) {
              callback(new Error('大陆身份证号长度必须为18位'))
              return
            }
            
            // 大陆身份证号格式校验
            const reg = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/
            if (!reg.test(value)) {
              callback(new Error('大陆身份证号格式不正确'))
              return
            }
            
            // 校验码验证
            const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
            const parity = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
            let sum = 0
            let ai = 0
            let wi = 0
            
            for (let i = 0; i < 17; i++) {
              ai = parseInt(value[i])
              wi = factor[i]
              sum += ai * wi
            }
            
            const last = parity[sum % 11]
            if (last !== value[17].toUpperCase()) {
              callback(new Error('大陆身份证号校验码错误'))
              return
            }
            // 计算性别
            this.calculateGender()
            callback()
          },
          trigger: ['blur']
        }],
        // photoUrl: {
        //   type: 'string',
        //   required: true,
        //   message: '请上传证件照',
        //   trigger: ['change']
        // },
        phonenumber: [
          { required: true, message: '请输入手机号码', trigger: ['blur'] },
          { pattern: /^1[3-9]\d{9}$/, message: '手机号码格式不正确', trigger: ['blur'] }
        ]
      },
      showIdCardModal: false,
      idCardImage: '',
      userTypeDict: [] // 用户类型字典
    }
  },
  computed: {
    // 判断是否为普通用户
    isGeneralUser() {
      const userInfo = this.$store.state.userInfo;
      if (!userInfo || !userInfo.userType) {
        return true; // 没有用户信息或用户类型时，默认为普通用户
      }
      // 支持逗号分割的用户类型，例如 'aider,mentor'
      const userTypes = userInfo.userType.split(',').map(type => type.trim());
      return userTypes.includes('general');
    },
    // 非普通用户只能编辑学历
    isFieldDisabled() {
      // 普通用户可以编辑所有字段
      if (this.isGeneralUser) {
        return false;
      }

      // 非普通用户：检查是否已经保存过完整的个人信息
      // 从globalData中获取已保存的信息来判断，只有保存后才禁用编辑
      const savedUserInfo = app?.globalData?.realUserInfo;
      if (savedUserInfo && savedUserInfo.realName && savedUserInfo.idCard && savedUserInfo.phonenumber) {
        return true;
      }

      // 如果还未保存过完整信息，允许编辑
      return false;
    },
    // 获取用户类型显示文本
    userTypeDisplayText() {
      const userInfo = this.$store.state.userInfo;
      if (!userInfo || !userInfo.userType) {
        return '普通用户';
      }
      return getUserTypeText(userInfo.userType, this.userTypeDict) || '普通用户';
    }
  },
  created() {
    this.formData.userId = this.$store.state.userInfo.userId
    // 同时加载学历字典和用户类型字典
    Promise.all([
      getDicts('education_option'),
      loadUserTypeDict()
    ]).then(([educationRes, userTypeDict]) => {
      this.educationOptions = educationRes.data.map(item => ({
        label: item.dictLabel,
        value: item.dictValue
      }))
      this.userTypeDict = userTypeDict
    }).then(()=>this.getPersonalInfo())
  },
  onShow() {
    app = getApp()
  },
  onLoad(options) {
    this.$refs.uForm.setRules(this.rules)
  },
  methods: {
    hideKeyboard() {
      uni.hideKeyboard()
    },
    handleGenderClick() {
      // 非普通用户不能编辑性别
      if (this.isFieldDisabled) {
        uni.showToast({
          title: `${this.userTypeDisplayText}个人信息已保存，仅可编辑学历`,
          icon: 'none'
        })
        return
      }
      if (!this.formData.idCard) {
        uni.showToast({
          title: '请先输入证件号',
          icon: 'none'
        })
        return
      }
      this.showSex = true
      this.hideKeyboard()
    },
    getPersonalInfo() {
      this.formData = app.globalData.realUserInfo || {
        userId: this.$store.state.userInfo.userId,
        realName: '',
        idCard: '',
        sex: '',
        education: '',
        companyName: '',
        photoUrl: '',
        photoId: ''
      }
      if (this.formData.idCard) {
        this.uploadStatus = true
      }
      this.updateSexText()
      this.updateEducationText()
    },
    goBack() {
      uni.navigateBack({ delta: 1 })
    },
    calculateGender() {
      // 如果身份证为空，则不进行性别计算
      if (!this.formData.idCard) {
        return
      }
      if (this.formData.idCard.length === 18) {
        const genderCode = parseInt(this.formData.idCard.charAt(16))
        this.formData.sex = genderCode % 2 === 1 ? 0 : 1
        this.updateSexText()
      }
    },
    updateSexText() {
      if(this.formData.sex !== undefined && this.formData.sex !== null) {
        let sex = Number(this.formData.sex);
        this.sexText = sex === 0 ? '男' : sex === 1 ? '女' : ''
        this.$refs.uForm.validateField('sex')
      }
    },
    updateEducationText() {
      this.educationText = this.educationOptions.find(item => item.value === this.formData.education)?.label || ''
    },
    onEducationConfirm(e) {
      this.educationText = e.value[0].label
      this.formData.education = e.value[0].value
      this.showEducationPicker = false
    },
    chooseImage() {
      uni.chooseImage({
        count: 1,
        success: (res) => {
          this.uploadImage(res.tempFilePaths[0])
        }
      })
    },
    previewImage() {
      if (this.formData.photoUrl) {
        uni.previewImage({
          urls: [this.formData.photoUrl]
        })
      }
    },
    uploadImage(filePath) {
      this.uploadStatus = false
      uploadImage(filePath,(url,fileId) => {
        this.formData.photoUrl = url;
        this.formData.photoId = fileId;
        this.uploadStatus = true;
        this.$refs.uForm.validateField('photoUrl')
      }, (err) => {
        console.log(err)
      })
    },
    saveProfile() {
      this.$refs.uForm.validate().then(valid => {
        if (!valid) {
          uni.showToast({ title: '请填写必填项', icon: 'none' })
          return
        }
        /*if (!this.uploadStatus) {
          uni.showToast({ title: '请上传证件照', icon: 'none' })
          return
        }*/
      
        updatePersonalInfo(this.formData).then(res => {
          uni.showToast({ title: '保存成功', icon: 'success' })
          app.globalData.realUserInfo = this.formData
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        }).catch(() => {
          uni.showToast({ title: '保存失败', icon: 'error' })
        })
      })
    },
    sexSelect(e) {
      this.formData.sex = e.value
      this.updateSexText()
      this.$refs.uForm.validateField('sex')
    },
    deletePhoto() {
      uni.showModal({
        title: '提示',
        content: '确定要删除照片吗？',
        success: (res) => {
          if (res.confirm) {
            this.formData.photoUrl = ''
            this.uploadStatus = false
            this.$refs.uForm.validateField('photoUrl')
          }
        }
      })
    },
    chooseIdCardImage() {
      // 非普通用户不能使用身份证识别功能
      if (this.isFieldDisabled) {
        uni.showToast({
          title: `${this.userTypeDisplayText}个人信息已保存，仅可编辑学历`,
          icon: 'none'
        })
        return
      }
      uni.chooseImage({
        count: 1,
        success: (res) => {
          const filePath = res.tempFilePaths[0]
          this.recognizeIdCard(filePath)
        }
      })
    },
    recognizeIdCard(filePath) {
      if (!filePath) return
      uni.showLoading({ title: '识别中...' })
      uni.uploadFile({
        url: baseUrl + '/wx/user/idCardInfo',
        filePath: filePath,
        header: {
          'Authorization': 'Bearer ' + storage.get(ACCESS_TOKEN)
        },
        name: 'file',
        success: (res) => {
          uni.hideLoading()
          const result = JSON.parse(res.data)
          if (result.code === 200 && result.data.type === 'Front' ) {
            const data = result.data;
            this.formData.realName = data.name
            this.formData.idCard = data.id
            this.formData.sex = data.gender === '男' ? 0 : 1
            this.updateSexText()
            uni.showToast({ title: '识别成功', icon: 'success' })
          } else {
            uni.showToast({ title: '识别失败', icon: 'error' })
          }
        },
        fail: () => {
          uni.hideLoading()
          uni.showToast({ title: '识别失败', icon: 'error' })
        }
      })
    }
  },
  onReady() {
    this.$refs.uForm.setRules(this.rules)
  }
}
</script>

<style lang="scss" scoped>
/* 用户类型提示样式 */
.user-type-tips {
  margin: 24rpx 40rpx 0 40rpx;
  padding: 20rpx 24rpx;
  background: #fff7e6;
  border: 2rpx solid #ffd591;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
}

.tips-text {
  margin-left: 16rpx;
  font-size: 26rpx;
  color: #d48806;
  line-height: 1.5;
}

.photo-upload {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #dcdfe6;
  border-radius: 8rpx;
  overflow: hidden;
}

.preview-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.delete-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom-left-radius: 8rpx;
}

.upload-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

.upload-text {
  font-size: 24rpx;
  color: #909399;
  margin-top: 10rpx;
}

.idcard-modal {
  padding: 32rpx;
  text-align: center;
}
.idcard-modal-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
}
</style> 