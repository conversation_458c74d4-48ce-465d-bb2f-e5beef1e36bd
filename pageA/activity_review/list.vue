<template>
  <view class="page-container">
    <Navbar :hideBtn="false" bgColor="#f3f4f6" title="活动上报列表" :fixed="false"></Navbar>

    <!-- 搜索筛选组件 -->
    <SearchFilter
      search-placeholder="搜索活动主题"
      search-key="activityTitle"
      :initial-params="searchParams"
      @change="onSearchChange"
    />

    <view class="list-container">
      <DataList
        ref="dataList"
        :labels="labels"
        :data="listData"
        :loading="loading"
        :pagination="pagination"
        @refresh="onRefresh"
        @load-more="onLoadMore"
        @item-click="onItemClick"
        :rightOptions="rightOptions"
        @swipe-action="onSwipeAction"
      />
    </view>
    <button class="fab-add" @click="onAddActivity">新增活动上报</button>
  </view>
</template>

<script>
import Navbar from "@/components/navbar/Navbar";
import DataList from "@/components/data-list/DataList";
import SearchFilter from "@/components/search-filter/SearchFilter.vue";
import { getActivityReviewList, deleteActivityReview } from "@/api/activity_review/activity_review.js";

export default {
  components: { Navbar, DataList, SearchFilter },
  data() {
    return {
      labels: [
        { label: '活动主题', prop: 'activityTitle' },
        { label: '开始日期', prop: 'startDate' },
        { label: '结束日期', prop: 'endDate' },
        { label: '讲师姓名', prop: 'lecturerName' },
        { label: '主办人', prop: 'sponsorUsersText' },
        { label: '协办人', prop: 'cooperateUsersText' },
        { label: '审核状态', prop: 'reviewStatus' },
        { label: '创建时间', prop: 'createTime' },
      ],
      listData: [],
      loading: false,
      pagination: {
        page: 1,
        pageSize: 10,
        total: 0,
      },
      // 搜索参数
      searchParams: {
        activityTitle: '',
        lecturerName: '',
        reviewStatus: '',
        startDate: '',
        endDate: ''
      },
      // 筛选字段配置
      /*filterFields: [
        {
          key: 'lecturerName',
          label: '讲师姓名',
          type: 'input',
          placeholder: '请输入讲师姓名'
        },
        {
          key: 'reviewStatus',
          label: '审核状态',
          type: 'radio',
          options: [
            { label: '全部', value: '' },
            { label: '草稿', value: '1' },
            { label: '待审核', value: '2' },
            { label: '已通过', value: '3' },
            { label: '已拒绝', value: '4' }
          ]
        },
        {
          key: 'startDate',
          label: '开始时间',
          type: 'date',
          placeholder: '请选择开始时间'
        },
        {
          key: 'endDate',
          label: '结束时间',
          type: 'date',
          placeholder: '请选择结束时间'
        }
      ],*/
      // 右侧操作按钮
      rightOptions: [
        {text: '删除', style: {backgroundColor: '#ff4d4f', color: '#fff'}}
      ],
      // 状态映射
      statusMap: {
        1: '草稿',
        2: '待审核',
        3: '已通过',
        4: '已拒绝'
      }
    };
  },
  onShow() {
    this.getList();
  },
  methods: {
    // 获取活动评审列表
    async getList(append = false) {
      if (!append) {
        this.pagination.page = 1;
      }

      this.loading = true;
      try {
        const params = {
          pageNum: this.pagination.page,
          pageSize: this.pagination.pageSize,
          ...this.searchParams
        };

        const response = await getActivityReviewList(params);
        const { rows, total } = response;

        // 处理数据，添加状态文本
        const processedRows = rows.map(item => ({
          ...item,
          reviewStatus: this.statusMap[item.reviewStatus] || '未知',
          // 处理主办人和协办人数据
          sponsorUsersText: this.formatUsers(item.organizers, 0),
          cooperateUsersText: this.formatUsers(item.assistants, 1)
        }));

        if (append) {
          this.listData = this.listData.concat(processedRows);
        } else {
          this.listData = processedRows;
        }

        this.pagination.total = total;
      } catch (error) {
        console.error('获取活动列表失败:', error);
        uni.$u.toast('获取活动列表失败');
      } finally {
        this.loading = false;
        // 停止 DataList 组件的刷新状态
        if (this.$refs.dataList) {
          this.$refs.dataList.stopRefresh();
        }
      }
    },
    // 点击列表项
    onItemClick(item) {
      uni.navigateTo({
        url: `/pageA/activity_review/form?id=${item.id}`,
      });
    },

    // 搜索参数变化
    onSearchChange(params) {
      this.searchParams = params;
      this.pagination.page = 1;
      this.getList(false);
    },

    // 下拉刷新
    onRefresh() {
      this.pagination.page = 1;
      this.getList(false);
    },

    // 加载更多
    onLoadMore() {
      if (this.pagination.page < Math.ceil(this.pagination.total / this.pagination.pageSize)) {
        this.pagination.page++;
        this.getList(true);
      }
    },

    // 新增活动
    onAddActivity() {
      uni.navigateTo({ url: '/pageA/activity_review/form' });
    },

    // 格式化用户数据，用逗号拼接姓名
    formatUsers(users, roleType) {
      if (!users || !Array.isArray(users)) return '';

      // 过滤指定角色类型的用户
      const filteredUsers = users.filter(user => user.roleType === roleType);

      // 提取用户姓名并用逗号拼接
      const userNames = filteredUsers.map(user => user.userName || user.name).filter(name => name);

      return userNames.join('，');
    },

    // 滑动操作按钮点击
    onSwipeAction({ action, item, index }) {
      if (action === '删除') {
        // 检查是否可以删除：只能删除草稿状态（1）和驳回状态（4）的活动
        const allowedStatuses = [1, 4]; // 1=草稿, 4=驳回
        if (!allowedStatuses.includes(item.reviewStatus)) {
          const statusText = this.statusMap[item.reviewStatus] || '未知';
          uni.showToast({
            title: `当前状态为"${statusText}"，只能删除草稿或驳回状态的活动`,
            icon: 'none',
            duration: 3000
          });
          return;
        }
        this.confirmDelete(item);
      }
    },

    // 确认删除
    confirmDelete(item) {
      // 检查权限：只能删除草稿状态（1）和驳回状态（4）的活动
      const allowedStatuses = [1, 4]; // 1=草稿, 4=驳回
      if (!allowedStatuses.includes(item.reviewStatus)) {
        const statusText = this.statusMap[item.reviewStatus] || '未知';
        uni.showToast({
          title: `当前状态为"${statusText}"，只能删除草稿或驳回状态的活动`,
          icon: 'none',
          duration: 3000
        });
        return;
      }

      const statusText = this.statusMap[item.reviewStatus] || '未知';
      uni.showModal({
        title: '确认删除',
        content: `确定要删除状态为"${statusText}"的活动"${item.activityTitle}"吗？`,
        success: (res) => {
          if (res.confirm) {
            this.deleteActivity(item.id);
          }
        }
      });
    },

    // 删除活动
    async deleteActivity(id) {
      try {
        await deleteActivityReview(id);
        uni.$u.toast('删除成功');
        this.getList(false);
      } catch (error) {
        console.error('删除活动失败:', error);
        uni.$u.toast('删除失败');
      }
    },


  },
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
}

.list-container {
  flex: 1;
  padding: 0 16rpx;
}

.fab-add {
  position: fixed;
  right: 48rpx;
  bottom: 120rpx;
  z-index: 99;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 16rpx;
  min-width: 200rpx;
  height: 80rpx;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx #bbb;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
}


</style>
