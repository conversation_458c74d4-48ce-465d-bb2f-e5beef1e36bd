<template>
	<view class="page-container">
		<Navbar :hideBtn="false" bgColor="#f3f4f6" title="急救案例列表" :fixed="false"></Navbar>
		<view class="search-bar">
			<u-search
				v-model="searchValue"
				placeholder="搜索被救助人姓名"
				:show-action="true"
				action-text="搜索"
				@search="onSearch"
				@custom="onSearch"
				bg-color="#ffffff"
			/>
		</view>
		<view class="list-container">
			<DataList
				ref="dataList"
				:labels="labels"
				:data="listData"
				:loading="loading"
				:pagination="pagination"
				@refresh="onRefresh"
				@load-more="onLoadMore"
				@item-click="onItemClick"
				@swipe-action="onSwipeAction"
				:rightOptions="rightOptions"
			/>
		</view>
		<button class="fab-add" @click="onAddCase">新增急救案例</button>
	</view>
</template>

<script>
	import Navbar from '@/components/navbar/Navbar.vue';
	import DataList from '@/components/data-list/DataList.vue';
	import { getRescueCaseList, deleteRescueCase } from '@/api/center/rescue_case/rescue_case.js';
	import { getDicts } from '@/api/system/dict/data.js';

	export default {
		components: { Navbar, DataList },
		data() {
			return {
				labels: [
					{ label: '姓名', prop: 'patientName' },
					{ label: '急救日期', prop: 'rescueDate' },
					{ label: '所在城市', prop: 'cityText' },
					{ label: '病症类型', prop: 'illnessType' },
					{ label: '申请状态', prop: 'statusText' },
				],
				listData: [],
				loading: false,
				pagination: {
					page: 1,
					pageSize: 10,
					total: 0,
				},
				searchValue: '',
				rightOptions: [
					{text: '删除', style: {backgroundColor: '#ff4d4f', color: '#fff'}}
				],
				illnessTypeDict: [] // 病症类型字典
			};
		},
		onShow() {
			this.loadIllnessTypeDict().then(() => {
				this.getList();
			});
		},
		methods: {
			async getList(append = false) {
				this.loading = true;

				try {
					const params = {
						pageNum: this.pagination.page,
						pageSize: this.pagination.pageSize,
						patientName: this.searchValue // 搜索参数
					};

					const response = await getRescueCaseList(params);

					if (response.code === 200) {
						const { rows, total } = response.data;

						// 处理数据，添加状态文本和病症类型文本
						const processedRows = rows.map(item => ({
							...item,
							statusText: this.getStatusText(item.rescueStatus),
							cityText: item.cityText || item.city, // 兼容不同的字段名
							illnessType: this.getIllnessTypeText(item.illnessType) // 转换病症类型显示
						}));

						this.pagination.total = total;

						if (append) {
							this.listData = this.listData.concat(processedRows);
						} else {
							this.listData = processedRows;
						}
					} else {
						uni.showToast({
							title: response.msg || '获取数据失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('获取急救案例列表失败:', error);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				} finally {
					this.loading = false;
					// 停止 DataList 组件的刷新状态
					if (this.$refs.dataList) {
						this.$refs.dataList.stopRefresh();
					}
				}
			},
			onItemClick(item) {
				uni.navigateTo({
					url: `/pageA/rescue_case/form?id=${item.id}`,
				});
			},
			onSearch() {
				this.pagination.page = 1;
				this.getList(false);
			},
			onRefresh() {
				this.pagination.page = 1;
				this.getList(false);
			},
			onLoadMore() {
				if (this.pagination.page < Math.ceil(this.pagination.total / this.pagination.pageSize)) {
					this.pagination.page++;
					this.getList(true);
				}
			},
			async onSwipeAction({action, item, index}) {
				if (action === '删除') {
					// 只有草稿状态(1)和驳回状态(4)才能删除
					if (item.rescueStatus !== '1' && item.rescueStatus !== '4') {
						uni.showToast({title: '只有草稿和驳回状态的案例可以删除', icon:'none'});
						return;
					}

					uni.showModal({
						title: '确认删除',
						content: '确定要删除这个急救案例吗？',
						success: async (res) => {
							if (res.confirm) {
								try {
									const response = await deleteRescueCase(item.id);
									if (response.code === 200) {
										this.listData.splice(index, 1);
										this.pagination.total--;
										uni.showToast({ title: '删除成功', icon: 'success' });
									} else {
										uni.showToast({
											title: response.msg || '删除失败',
											icon: 'none'
										});
									}
								} catch (error) {
									console.error('删除急救案例失败:', error);
									uni.showToast({
										title: '删除失败，请重试',
										icon: 'none'
									});
								}
							}
						}
					});
				}
			},
			getStatusText(status) {
				const statusMap = {
					'1': '草稿',
					'2': '审核中',
					'3': '已通过',
					'4': '已驳回'
				};
				return statusMap[status] || '未知状态';
			},
			onAddCase() {
				uni.navigateTo({ url: '/pageA/rescue_case/form' });
			},
			// 加载病症类型字典
			async loadIllnessTypeDict() {
				try {
					const res = await getDicts('rescue_disease_type');
					this.illnessTypeDict = res.data || [];
				} catch (error) {
					console.error('获取病症类型字典失败:', error);
					this.illnessTypeDict = [];
				}
			},
			// 获取病症类型显示文本
			getIllnessTypeText(value) {
				if (!value) return '';
				const dictItem = this.illnessTypeDict.find(item => item.dictValue === value);
				return dictItem ? dictItem.dictLabel : value;
			},
		},
	};
</script>

<style lang="scss" scoped>
	.page-container {
		height: 100vh;
		display: flex;
		flex-direction: column;
		position: relative;
		overflow: hidden;
		background-color: #f5f5f5;
	}

	.search-bar {
		display: flex;
		align-items: center;
		padding: 16rpx;
		background: #fff;
		border-radius: 8rpx;
		margin: 16rpx;
		gap: 16rpx;
		flex-shrink: 0; /* 防止搜索栏被压缩 */
	}

	.list-container {
		flex: 1;
		padding: 0 16rpx;
	}

	.fab-add {
		position: fixed;
		right: 48rpx;
		bottom: 120rpx;
		z-index: 99;
		background: #007aff;
		color: #fff;
		border: none;
		border-radius: 16rpx;
		min-width: 180rpx;
		height: 80rpx;
		font-size: 32rpx;
		box-shadow: 0 4rpx 16rpx #bbb;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 32rpx;
	}
</style> 