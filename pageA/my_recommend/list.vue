<template>
	<view class="page-container">
		<Navbar :hideBtn="false" bgColor="#f3f4f6" title="我的感召" :fixed="false"></Navbar>
		<view class="search-bar">
			<u-search
				v-model="searchValue"
				placeholder="搜索用户姓名"
				:show-action="true"
				action-text="搜索"
				@search="onSearch"
				@custom="onSearch"
				bg-color="#ffffff"
			/>
		</view>
		<view class="list-container">
			<DataList
				ref="dataList"
				:labels="labels"
				:data="listData"
				:loading="loading"
				:pagination="pagination"
				@refresh="onRefresh"
				@load-more="onLoadMore"
				@item-click="onItemClick"
			/>
		</view>
	</view>
</template>

<script>
	import Navbar from '@/components/navbar/Navbar.vue';
	import DataList from '@/components/data-list/DataList.vue';
	import { getMyRecommendList } from '@/api/my_recommend/my_recommend.js';
	import { loadUserTypeDict, getUserTypeText } from '@/utils/dictUtils.js';

	export default {
		components: { Navbar, DataList },
		data() {
			return {
				labels: [
					{ label: '用户姓名', prop: 'realName' },
					{ label: '用户类型', prop: 'userTypeText' },
					{ label: '报名时间', prop: 'upgradedTime' }
				],
				listData: [],
				loading: false,
				pagination: {
					page: 1,
					pageSize: 5,
					total: 0,
				},
				searchValue: '',
				userTypeDict: [] // 用户类型字典
			};
		},
		onShow() {
			loadUserTypeDict().then(dictData => {
				this.userTypeDict = dictData;
				this.getList();
			});
		},
		methods: {
			async getList(append = false) {
				this.loading = true;

				try {
					const params = {
						pageNum: this.pagination.page,
						pageSize: this.pagination.pageSize,
						realName: this.searchValue // 搜索参数
					};

					const response = await getMyRecommendList(params);

					if (response.code === 200) {
						const { rows, total } = response.data;

						// 处理数据，添加用户类型文本
						const processedRows = rows.map(item => ({
							...item,
							userTypeText: getUserTypeText(item.userType, this.userTypeDict)
						}));

						this.pagination.total = total;

						if (append) {
							this.listData = this.listData.concat(processedRows);
						} else {
							this.listData = processedRows;
						}
					} else {
						uni.showToast({
							title: response.msg || '获取数据失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('获取我的感召列表失败:', error);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				} finally {
					this.loading = false;
					// 停止 DataList 组件的刷新状态
					if (this.$refs.dataList) {
						this.$refs.dataList.stopRefresh();
					}
				}
			},
			onItemClick(item) {
				// 可以根据需要添加点击事件处理
				console.log('点击了用户:', item);
			},
			onSearch() {
				this.pagination.page = 1;
				this.getList(false);
			},
			onRefresh() {
				this.pagination.page = 1;
				this.getList(false);
			},
			onLoadMore() {
				if (this.pagination.page < Math.ceil(this.pagination.total / this.pagination.pageSize)) {
					this.pagination.page++;
					this.getList(true);
				}
			}
		},
	};
</script>

<style lang="scss" scoped>
	.page-container {
		height: 100vh;
		display: flex;
		flex-direction: column;
		position: relative;
		overflow: hidden;
		background-color: #f5f5f5;
	}

	.search-bar {
		display: flex;
		align-items: center;
		padding: 16rpx;
		background: #fff;
		border-radius: 8rpx;
		margin: 16rpx;
		gap: 16rpx;
		flex-shrink: 0; /* 防止搜索栏被压缩 */
	}

	.list-container {
		flex: 1;
		padding: 0 16rpx;
	}

	.empty-text {
		color: #888;
		font-size: 32rpx;
	}
</style>
