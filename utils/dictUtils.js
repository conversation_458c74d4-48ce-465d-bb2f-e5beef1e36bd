/**
 * 字典工具函数
 * 提供字典数据的加载和转换功能
 */

import { getDicts } from '@/api/system/dict/data.js';

/**
 * 加载用户类型字典
 * @returns {Promise<Array>} 用户类型字典数组
 */
export async function loadUserTypeDict() {
  try {
    const res = await getDicts('sys_user_type');
    return res.data || [];
  } catch (error) {
    console.error('获取用户类型字典失败:', error);
    return [];
  }
}

/**
 * 根据用户类型值获取显示文本
 * @param {string} value - 用户类型值（可能包含多个类型，逗号分隔）
 * @param {Array} dictData - 用户类型字典数据
 * @returns {string} 显示文本
 */
export function getUserTypeText(value, dictData = []) {
  if (!value) return '';

  // 处理多个用户类型（逗号分隔）
  const userTypes = value.split(',').map(type => type.trim());
  const typeLabels = userTypes.map(type => {
    const dictItem = dictData.find(item => item.dictValue === type);
    return dictItem ? dictItem.dictLabel : type;
  });

  return typeLabels.join('、');
}

/**
 * 通用字典加载方法
 * @param {string} dictType - 字典类型
 * @returns {Promise<Array>} 字典数据数组
 */
export async function loadDict(dictType) {
  try {
    const res = await getDicts(dictType);
    return res.data || [];
  } catch (error) {
    console.error(`获取字典${dictType}失败:`, error);
    return [];
  }
}

/**
 * 根据字典值获取显示文本
 * @param {string} value - 字典值
 * @param {Array} dictData - 字典数据
 * @param {string} valueField - 值字段名，默认为 'dictValue'
 * @param {string} labelField - 标签字段名，默认为 'dictLabel'
 * @returns {string} 显示文本
 */
export function getDictText(value, dictData = [], valueField = 'dictValue', labelField = 'dictLabel') {
  if (!value) return '';
  
  const dictItem = dictData.find(item => item[valueField] === value);
  return dictItem ? dictItem[labelField] : value;
}
